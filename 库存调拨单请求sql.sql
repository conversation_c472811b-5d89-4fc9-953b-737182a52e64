with
t1 as (select * from wm_move_h),
t2 as (select * from wm_move_b where wm_move_h_id in (select wm_move_h_id from t1)),
t3 as (select wm_move_h_id,part_no,part_qty,lot_no from wm_move_sn_part where wm_move_h_id in (select wm_move_h_id from t1)),
t4 as (select wm_move_h_id,part_no,lot_no,sum(part_qty) as part_qty_lot from t3 group by wm_move_h_id,part_no,lot_no),
t5 as (select t2.wm_move_h_id,t2.part_no,t2.part_qty_real,t2.move_from_invp_no,t2.move_to_invp_no,json_agg(json_build_object('BatchNo',t4.lot_no,'Qty',t4.part_qty_lot)) as part_qty_lot 
	from t2 left join t4 on t4.wm_move_h_id=t2.wm_move_h_id and t4.part_no=t2.part_no
	group by t2.wm_move_h_id,t2.part_no,t2.part_qty_real,t2.move_from_invp_no,t2.move_to_invp_no),
t6 as (select t5.wm_move_h_id,json_agg(json_build_object('ItemCode',t5.part_no,'QuanTity',t5.part_qty_real,'BatchNo',t5.part_qty_lot,
		'FromWhsCod',t5.move_from_invp_no,'WhsCode',t5.move_to_invp_no)) as details_datas
	from t5 group by wm_move_h_id)
select 
t1.wm_move_h_no,
json_build_object(
'DocDate',t1.move_datetime,
'Filler',t1.move_from_invp_no,
'ToWhsCode',t1.move_to_invp_no,
'U_WebNo',t1.wm_move_h_no,
'details',t6.details_datas) as datas 
from t1
left join t6 on t6.wm_move_h_id=t1.wm_move_h_id
