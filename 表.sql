
CREATE TABLE public.wm_move_h (
	wm_move_h_id text DEFAULT af_auid() NOT NULL,
	wm_move_h_no text DEFAULT af_ss_no_generate('wm_move_h_no'::character varying) NOT NULL,
	wm_move_status text NULL,
	move_datetime date DEFAULT CURRENT_DATE NOT NULL,
	move_from_invp_no text NOT NULL,
	move_from_invp_name text NULL,
	move_to_invp_no text NOT NULL,
	move_to_invp_name text NULL,
	move_reason_no text NULL,
	move_reason_name text NULL,
	move_dept_no text NULL,
	move_dept_name text NULL,
	wm_move_rmk01 text NULL,
	wm_move_rmk02 text NULL,
	wm_move_rmk03 text NULL,
	wm_move_rmk04 text NULL,
	wm_move_rmk05 text NULL,
	wm_move_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	applycode int4 DEFAULT 0 NULL,
	applymessage text NULL,
	sap_bill_no text NULL,
	CONSTRAINT wm_move_h_pk PRIMARY KEY (wm_move_h_id),
	CONSTRAINT wm_move_h_unique UNIQUE (wm_move_h_no)
);



CREATE TABLE public.wm_move_b (
	wm_move_b_id text DEFAULT af_auid() NOT NULL,
	wm_move_h_id text NOT NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_qty_plan numeric DEFAULT 0 NOT NULL,
	part_qty_real numeric DEFAULT 0 NOT NULL,
	lot_no text NULL,
	move_from_invp_no text NOT NULL,
	move_to_invp_no text NOT NULL,
	wm_move_rmk01 text NULL,
	wm_move_rmk02 text NULL,
	wm_move_rmk03 text NULL,
	wm_move_rmk04 text NULL,
	wm_move_rmk05 text NULL,
	wm_move_rmk06 text NULL,
	crt_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	crt_user text NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NULL,
	upd_time timestamp DEFAULT LOCALTIMESTAMP NOT NULL,
	upd_user text NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NULL,
	CONSTRAINT wm_move_b_pk PRIMARY KEY (wm_move_b_id)
);


CREATE TABLE public.wm_move_sn_part (
	wm_move_sn_part_id text DEFAULT af_auid() NOT NULL,
	wm_move_h_id text NOT NULL,
	sn_no text NULL,
	part_no text NULL,
	part_name text NULL,
	part_spec text NULL,
	part_unit text NULL,
	part_unit_name text NULL,
	part_idt text NULL,
	mrp_region_no text NULL,
	lot_no text NULL,
	part_qty numeric(18, 4) NULL,
	crt_time timestamp NOT NULL,
	crt_user text NOT NULL,
	crt_user_no text NULL,
	crt_user_name text NULL,
	crt_host text NOT NULL,
	upd_time timestamp NOT NULL,
	upd_user text NOT NULL,
	upd_user_no text NULL,
	upd_user_name text NULL,
	upd_host text NOT NULL,
	CONSTRAINT wm_move_sn_part_pkey PRIMARY KEY (wm_move_sn_part_id)
)
PARTITION BY HASH (wm_move_sn_part_id);
CREATE INDEX ix_wm_move_sn_part_sn_no ON ONLY public.wm_move_sn_part USING btree (sn_no);
